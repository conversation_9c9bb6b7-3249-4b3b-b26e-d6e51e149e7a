# 飞书MCP高级配置指南

## 问题分析

当前的MCP配置无法调用文档块API (`/open-apis/docx/v1/documents/{document_id}/blocks`)，主要原因：

1. **工具集配置不完整**：当前只使用了基础的`preset.default`
2. **权限不足**：应用可能缺少文档读取权限
3. **认证模式**：可能需要OAuth用户授权

## 解决方案

### 1. 检查应用权限

在飞书开放平台控制台中，确保您的应用具有以下权限：

```
- docs:read (文档读取)
- docs:write (文档写入，如果需要)
- bitable:read (多维表格读取)
- wiki:read (知识库读取)
- drive:read (云文档读取)
```

### 2. 使用OAuth模式配置

创建OAuth配置文件 `mcp-oauth-config.json`：

```json
{
  "appId": "cli_a8fe79f1109d100b",
  "appSecret": "d2EfbzCwJpKJtZYV0STPYcGpwLFsnVjE",
  "domain": "https://open.feishu.cn",
  "tokenMode": "user_access_token",
  "oauth": true,
  "scope": "docs:read docs:write bitable:read wiki:read drive:read",
  "tools": [
    "preset.all",
    "docx_v1_document_block_list",
    "docx_v1_document_block_get",
    "docx_v1_document_block_children_list",
    "docx_v1_document_rawContent"
  ],
  "toolNameCase": "snake",
  "language": "zh",
  "mode": "stdio",
  "debug": true
}
```

### 3. 启动命令

```bash
# 方式1：使用配置文件
lark-mcp mcp --config mcp-oauth-config.json

# 方式2：直接命令行
lark-mcp mcp \
  -a cli_a8fe79f1109d100b \
  -s d2EfbzCwJpKJtZYV0STPYcGpwLFsnVjE \
  --token-mode user_access_token \
  --oauth \
  --scope "docs:read docs:write bitable:read wiki:read drive:read" \
  --tools "preset.all,docx_v1_document_block_list" \
  --debug
```

### 4. 可能需要的具体工具

根据文档块API的需求，确保启用以下工具：

```
- docx_v1_document_block_list (获取文档块列表)
- docx_v1_document_block_get (获取单个文档块)
- docx_v1_document_block_children_list (获取子块列表)
- docx_v1_document_rawContent (获取纯文本内容)
- wiki_v2_space_getNode (获取Wiki节点信息)
```

### 5. 测试步骤

1. **启动MCP服务器**：
   ```bash
   lark-mcp mcp --config mcp-oauth-config.json
   ```

2. **检查可用工具**：
   服务器启动后应该显示所有可用的API工具

3. **测试文档块API**：
   ```bash
   # 测试获取文档块
   curl -X GET \
     "https://open.feishu.cn/open-apis/docx/v1/documents/B4LudmzddoWV7txI1rMc46VunMd/blocks" \
     -H "Authorization: Bearer YOUR_USER_ACCESS_TOKEN"
   ```

### 6. 常见问题排查

#### 问题1：权限不足
```
错误: {"code":99991663,"msg":"permission denied"}
```
**解决**：检查应用权限配置，确保有`docs:read`权限

#### 问题2：Token无效
```
错误: {"code":99991661,"msg":"token invalid"}
```
**解决**：使用OAuth模式重新获取用户授权

#### 问题3：文档不存在或无权限
```
错误: {"code":1254006,"msg":"document not found"}
```
**解决**：确认文档ID正确，且用户有访问权限

### 7. 完整的API调用示例

```python
import requests

# 使用OAuth获取的用户访问令牌
user_access_token = "YOUR_USER_ACCESS_TOKEN"

# 文档ID
document_id = "B4LudmzddoWV7txI1rMc46VunMd"

# 获取文档块
url = f"https://open.feishu.cn/open-apis/docx/v1/documents/{document_id}/blocks"
headers = {
    "Authorization": f"Bearer {user_access_token}",
    "Content-Type": "application/json"
}

response = requests.get(url, headers=headers)
print(response.json())
```

## 下一步行动

1. 检查飞书应用权限配置
2. 使用OAuth模式重新配置MCP
3. 测试文档块API调用
4. 如果仍有问题，检查用户对目标文档的访问权限
