#!/usr/bin/env python3
"""
测试飞书文档块API
需要先通过OAuth获取用户访问令牌
"""

import requests
import json
import sys

def test_document_blocks_api(user_access_token, document_id):
    """测试文档块API"""
    
    base_url = "https://open.feishu.cn/open-apis"
    headers = {
        "Authorization": f"Bearer {user_access_token}",
        "Content-Type": "application/json"
    }
    
    print(f"🔍 测试文档ID: {document_id}")
    print(f"🔑 使用令牌: {user_access_token[:20]}...")
    print("")
    
    # 测试1: 获取文档基本信息
    print("📄 测试1: 获取文档基本信息")
    try:
        url = f"{base_url}/docx/v1/documents/{document_id}"
        response = requests.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 文档信息获取成功")
            data = response.json()
            print(f"文档标题: {data.get('data', {}).get('document', {}).get('title', 'N/A')}")
        else:
            print(f"❌ 失败: {response.text}")
    except Exception as e:
        print(f"❌ 异常: {e}")
    
    print("")
    
    # 测试2: 获取文档块列表
    print("🧱 测试2: 获取文档块列表")
    try:
        url = f"{base_url}/docx/v1/documents/{document_id}/blocks"
        response = requests.get(url, headers=headers, params={"page_size": 10})
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 文档块获取成功")
            data = response.json()
            blocks = data.get('data', {}).get('items', [])
            print(f"块数量: {len(blocks)}")
            for i, block in enumerate(blocks[:3]):
                print(f"  块{i+1}: {block.get('block_type', 'unknown')} - {block.get('block_id', 'N/A')}")
        else:
            print(f"❌ 失败: {response.text}")
    except Exception as e:
        print(f"❌ 异常: {e}")

def main():
    """主函数"""
    print("飞书文档块API测试工具")
    print("=" * 40)
    
    # 从命令行参数获取令牌
    if len(sys.argv) > 1:
        user_access_token = sys.argv[1]
    else:
        user_access_token = input("请输入用户访问令牌: ").strip()
    
    if not user_access_token:
        print("❌ 未提供用户访问令牌")
        return
    
    document_id = "B4LudmzddoWV7txI1rMc46VunMd"
    test_document_blocks_api(user_access_token, document_id)

if __name__ == "__main__":
    main()
