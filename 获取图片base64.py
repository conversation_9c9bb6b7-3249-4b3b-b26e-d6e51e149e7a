#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取飞书文档图片的base64编码
使用临时下载链接获取图片并转换为base64格式
"""

import requests
import base64
import json
from pathlib import Path

# 图片信息配置
IMAGES_INFO = [
    {
        "name": "功能点1-交互图片",
        "token": "Ge64bwGE5o4wPLxf1alcXE1cnfu",
        "url": "https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=OTlmOTA5YjE3NzIxZmQ4ZGMzZDhkM2MzNGQwN2ZkMWRfZmE0OTI1ZWNkYjgwYjVkYTVjN2FiMmQxNzIyNjI1ZTRfSUQ6NzUyNjc3NzczMDQ4MzQ1Mzk3MV8xNzU0MDE2MDgwOjE3NTQxMDI0ODBfVjM",
        "size": "879x480"
    },
    {
        "name": "功能点2-交互图片", 
        "token": "LJwNbTsakodGOzxGbbOcUu5Snob",
        "url": "https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=MTNkZmFiYzIwZDU4NzUxMDUyMzU2M2VhN2YzYzFkNzVfZjAyNmYyYTdmNjg3YjQ2OTE3YjNlMWVkMDhjYzJkOTFfSUQ6NzUyNjc3NzgwMjA2Mzg3MjAyOF8xNzU0MDE2MDgwOjE3NTQxMDI0ODBfVjM",
        "size": "1002x486"
    }
]

def download_and_encode_image(image_info):
    """
    下载图片并转换为base64编码
    
    Args:
        image_info (dict): 图片信息字典
        
    Returns:
        tuple: (base64_string, file_size)
    """
    try:
        print(f"正在下载图片: {image_info['name']}")
        
        # 下载图片
        response = requests.get(image_info['url'], timeout=30)
        response.raise_for_status()
        
        # 获取图片数据
        image_data = response.content
        file_size = len(image_data)
        
        # 转换为base64
        base64_string = base64.b64encode(image_data).decode('utf-8')
        
        print(f"✅ 下载成功: {image_info['name']} ({file_size} bytes)")
        return base64_string, file_size
        
    except Exception as e:
        print(f"❌ 下载失败: {image_info['name']} - {str(e)}")
        return None, 0

def save_base64_files():
    """
    保存base64编码到文件
    """
    results = []
    
    for image_info in IMAGES_INFO:
        base64_data, file_size = download_and_encode_image(image_info)
        
        if base64_data:
            # 保存base64文件
            base64_filename = f"{image_info['name']}.base64"
            with open(base64_filename, 'w', encoding='utf-8') as f:
                f.write(base64_data)
            
            # 保存为data URL格式
            data_url_filename = f"{image_info['name']}-dataurl.txt"
            data_url = f"data:image/png;base64,{base64_data}"
            with open(data_url_filename, 'w', encoding='utf-8') as f:
                f.write(data_url)
            
            # 记录结果
            result = {
                "name": image_info['name'],
                "token": image_info['token'],
                "size": image_info['size'],
                "file_size": file_size,
                "base64_length": len(base64_data),
                "base64_file": base64_filename,
                "data_url_file": data_url_filename,
                "status": "success"
            }
            results.append(result)
            
            print(f"📁 已保存: {base64_filename}")
            print(f"📁 已保存: {data_url_filename}")
        else:
            result = {
                "name": image_info['name'],
                "token": image_info['token'],
                "status": "failed"
            }
            results.append(result)
    
    # 保存结果摘要
    summary = {
        "total_images": len(IMAGES_INFO),
        "successful": len([r for r in results if r.get('status') == 'success']),
        "failed": len([r for r in results if r.get('status') == 'failed']),
        "results": results
    }
    
    with open('base64转换结果.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 转换完成:")
    print(f"   总计: {summary['total_images']} 张图片")
    print(f"   成功: {summary['successful']} 张")
    print(f"   失败: {summary['failed']} 张")
    print(f"   结果已保存到: base64转换结果.json")

def create_html_preview():
    """
    创建HTML预览文件
    """
    html_content = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞书文档图片预览</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .image-container { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .image-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
        .image-info { margin: 10px 0; color: #666; }
        img { max-width: 100%; height: auto; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>飞书文档图片预览</h1>
    <p>文档来源: 前端执行方案模版 - 20250714试行</p>
"""
    
    for image_info in IMAGES_INFO:
        data_url_file = f"{image_info['name']}-dataurl.txt"
        if Path(data_url_file).exists():
            with open(data_url_file, 'r', encoding='utf-8') as f:
                data_url = f.read().strip()
            
            html_content += f"""
    <div class="image-container">
        <div class="image-title">{image_info['name']}</div>
        <div class="image-info">
            Token: {image_info['token']}<br>
            尺寸: {image_info['size']}
        </div>
        <img src="{data_url}" alt="{image_info['name']}">
    </div>
"""
    
    html_content += """
</body>
</html>
"""
    
    with open('图片预览.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("🌐 HTML预览文件已创建: 图片预览.html")

if __name__ == "__main__":
    print("🚀 开始获取飞书文档图片的base64编码...")
    print("=" * 50)
    
    # 保存base64文件
    save_base64_files()
    
    # 创建HTML预览
    create_html_preview()
    
    print("\n✨ 所有任务完成!")
