#!/usr/bin/env python3
"""
检查MCP服务器状态和可用API
"""

import requests
import json
import time

def check_mcp_server():
    """检查MCP服务器状态"""
    
    base_url = "http://localhost:3000"
    
    print("🔍 检查MCP服务器状态")
    print("=" * 40)
    
    # 检查服务器是否运行
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"✅ 服务器运行状态: {response.status_code}")
        if response.text:
            print(f"响应内容: {response.text[:200]}...")
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False
    
    # 检查SSE端点
    try:
        response = requests.get(f"{base_url}/sse", timeout=5)
        print(f"📡 SSE端点状态: {response.status_code}")
    except Exception as e:
        print(f"❌ SSE端点连接失败: {e}")
    
    # 检查OAuth状态
    try:
        response = requests.get(f"{base_url}/auth", timeout=5)
        print(f"🔐 OAuth端点状态: {response.status_code}")
        if response.text:
            print(f"OAuth响应: {response.text[:200]}...")
    except Exception as e:
        print(f"ℹ️ OAuth端点: {e}")
    
    return True

def test_direct_api():
    """直接测试飞书API"""
    
    print("\n🧪 直接测试飞书API")
    print("=" * 40)
    
    # 这里需要用户提供访问令牌
    print("注意: 需要有效的用户访问令牌才能测试API")
    print("请先完成OAuth授权获取令牌")
    
    # 测试文档信息API
    document_id = "B4LudmzddoWV7txI1rMc46VunMd"
    api_url = f"https://open.feishu.cn/open-apis/docx/v1/documents/{document_id}"
    
    print(f"📄 目标文档ID: {document_id}")
    print(f"🔗 API端点: {api_url}")
    
    # 提供测试命令
    print("\n📝 手动测试命令:")
    print("curl -X GET \\")
    print(f'  "{api_url}" \\')
    print('  -H "Authorization: Bearer YOUR_USER_ACCESS_TOKEN" \\')
    print('  -H "Content-Type: application/json"')

def main():
    """主函数"""
    print("飞书MCP状态检查工具")
    print("=" * 50)
    
    # 检查MCP服务器
    if check_mcp_server():
        print("\n✅ MCP服务器运行正常")
    else:
        print("\n❌ MCP服务器未运行或有问题")
        return
    
    # 测试API
    test_direct_api()
    
    print("\n🎯 下一步操作:")
    print("1. 在浏览器中访问 http://localhost:3000 进行OAuth授权")
    print("2. 获取用户访问令牌")
    print("3. 使用令牌测试文档块API")
    print("4. 如果成功，说明MCP配置正确")

if __name__ == "__main__":
    main()
