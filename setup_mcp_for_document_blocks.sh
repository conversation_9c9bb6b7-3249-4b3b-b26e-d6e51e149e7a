#!/bin/bash

# 飞书MCP文档块API配置脚本
# 用于正确配置MCP以支持文档块API调用

set -e

echo "🚀 飞书MCP文档块API配置脚本"
echo "=================================="

# 配置变量
APP_ID="cli_a8fe79f1109d100b"
APP_SECRET="d2EfbzCwJpKJtZYV0STPYcGpwLFsnVjE"
DOCUMENT_ID="B4LudmzddoWV7txI1rMc46VunMd"

echo "📋 配置信息:"
echo "  应用ID: $APP_ID"
echo "  文档ID: $DOCUMENT_ID"
echo ""

# 步骤1: 检查lark-mcp是否安装
echo "🔍 步骤1: 检查lark-mcp安装状态"
if command -v lark-mcp &> /dev/null; then
    echo "✅ lark-mcp已安装"
    lark-mcp --version 2>/dev/null || echo "版本信息不可用"
else
    echo "❌ lark-mcp未安装，请先安装:"
    echo "   npm install -g @larksuiteoapi/mcp"
    exit 1
fi

echo ""

# 步骤2: 测试基础连接
echo "🔗 步骤2: 测试基础MCP连接"
echo "启动基础MCP服务器（5秒后自动停止）..."

timeout 5s lark-mcp mcp \
    -a "$APP_ID" \
    -s "$APP_SECRET" \
    --token-mode user_access_token \
    --tools preset.default \
    --debug 2>&1 | head -20 || echo "基础连接测试完成"

echo ""

# 步骤3: 尝试OAuth模式
echo "🔐 步骤3: 配置OAuth模式"

cat > mcp-full-config.json << EOF
{
  "appId": "$APP_ID",
  "appSecret": "$APP_SECRET",
  "domain": "https://open.feishu.cn",
  "tokenMode": "user_access_token",
  "oauth": true,
  "scope": "docs:read docs:write bitable:read wiki:read drive:read",
  "tools": "preset.all",
  "toolNameCase": "snake",
  "language": "zh",
  "mode": "sse",
  "host": "localhost",
  "port": 3000,
  "debug": true
}
EOF

echo "✅ 已创建完整配置文件: mcp-full-config.json"

# 步骤4: 启动OAuth服务器
echo ""
echo "🌐 步骤4: 启动OAuth MCP服务器"
echo "注意: 这将启动一个Web服务器用于OAuth授权"
echo "请在浏览器中访问 http://localhost:3000 进行授权"
echo ""
echo "启动命令:"
echo "lark-mcp mcp --config mcp-full-config.json"
echo ""

# 步骤5: 提供手动测试命令
echo "🧪 步骤5: 手动测试命令"
echo ""
echo "方式1 - 使用配置文件:"
echo "lark-mcp mcp --config mcp-full-config.json"
echo ""
echo "方式2 - 直接命令行:"
echo "lark-mcp mcp \\"
echo "  -a $APP_ID \\"
echo "  -s $APP_SECRET \\"
echo "  --oauth \\"
echo "  --scope \"docs:read docs:write\" \\"
echo "  --tools \"preset.all\" \\"
echo "  --mode sse \\"
echo "  --port 3000 \\"
echo "  --debug"
echo ""

# 步骤6: 提供API测试脚本
echo "📝 步骤6: 创建API测试脚本"

cat > test_document_blocks_api.py << 'EOF'
#!/usr/bin/env python3
"""
测试飞书文档块API
需要先通过OAuth获取用户访问令牌
"""

import requests
import json
import sys

def test_document_blocks_api(user_access_token, document_id):
    """测试文档块API"""
    
    base_url = "https://open.feishu.cn/open-apis"
    headers = {
        "Authorization": f"Bearer {user_access_token}",
        "Content-Type": "application/json"
    }
    
    print(f"🔍 测试文档ID: {document_id}")
    print(f"🔑 使用令牌: {user_access_token[:20]}...")
    print("")
    
    # 测试1: 获取文档基本信息
    print("📄 测试1: 获取文档基本信息")
    try:
        url = f"{base_url}/docx/v1/documents/{document_id}"
        response = requests.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 文档信息获取成功")
            data = response.json()
            print(f"文档标题: {data.get('data', {}).get('document', {}).get('title', 'N/A')}")
        else:
            print(f"❌ 失败: {response.text}")
    except Exception as e:
        print(f"❌ 异常: {e}")
    
    print("")
    
    # 测试2: 获取文档块列表
    print("🧱 测试2: 获取文档块列表")
    try:
        url = f"{base_url}/docx/v1/documents/{document_id}/blocks"
        response = requests.get(url, headers=headers, params={"page_size": 10})
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 文档块获取成功")
            data = response.json()
            blocks = data.get('data', {}).get('items', [])
            print(f"块数量: {len(blocks)}")
            for i, block in enumerate(blocks[:3]):
                print(f"  块{i+1}: {block.get('block_type', 'unknown')} - {block.get('block_id', 'N/A')}")
        else:
            print(f"❌ 失败: {response.text}")
    except Exception as e:
        print(f"❌ 异常: {e}")

def main():
    """主函数"""
    print("飞书文档块API测试工具")
    print("=" * 40)
    
    # 从命令行参数获取令牌
    if len(sys.argv) > 1:
        user_access_token = sys.argv[1]
    else:
        user_access_token = input("请输入用户访问令牌: ").strip()
    
    if not user_access_token:
        print("❌ 未提供用户访问令牌")
        return
    
    document_id = "B4LudmzddoWV7txI1rMc46VunMd"
    test_document_blocks_api(user_access_token, document_id)

if __name__ == "__main__":
    main()
EOF

chmod +x test_document_blocks_api.py
echo "✅ 已创建API测试脚本: test_document_blocks_api.py"

echo ""
echo "🎯 下一步操作指南:"
echo "1. 运行: lark-mcp mcp --config mcp-full-config.json"
echo "2. 在浏览器中访问 http://localhost:3000 进行OAuth授权"
echo "3. 获取用户访问令牌后，运行: python test_document_blocks_api.py YOUR_TOKEN"
echo "4. 检查API调用结果"
echo ""
echo "📚 如果仍有问题，请检查:"
echo "- 飞书应用是否有docs:read权限"
echo "- 用户是否有访问目标文档的权限"
echo "- 网络连接是否正常"
