#!/usr/bin/env python3
"""
测试当前MCP配置下可用的API
"""

import json
import subprocess
import sys

def test_mcp_tools():
    """测试MCP工具配置"""
    
    # 测试不同的工具配置
    configs = [
        {
            "name": "default",
            "tools": "preset.default"
        },
        {
            "name": "all",
            "tools": "preset.all"
        },
        {
            "name": "docs",
            "tools": "preset.docs"
        },
        {
            "name": "docx",
            "tools": "preset.docx"
        },
        {
            "name": "specific_docx",
            "tools": "docx_v1_document_block_list,docx_v1_document_rawContent"
        }
    ]
    
    base_cmd = [
        "lark-mcp", "mcp",
        "-a", "cli_a8fe79f1109d100b",
        "-s", "d2EfbzCwJpKJtZYV0STPYcGpwLFsnVjE",
        "--token-mode", "user_access_token"
    ]
    
    for config in configs:
        print(f"\n=== 测试配置: {config['name']} ===")
        print(f"工具: {config['tools']}")
        
        cmd = base_cmd + ["--tools", config["tools"], "--debug"]
        
        try:
            # 启动进程并快速终止，查看输出
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待一小段时间让进程初始化
            try:
                stdout, stderr = process.communicate(timeout=3)
                print(f"输出: {stdout[:200]}...")
                if stderr:
                    print(f"错误: {stderr[:200]}...")
            except subprocess.TimeoutExpired:
                process.kill()
                stdout, stderr = process.communicate()
                print("进程超时，已终止")
                if stdout:
                    print(f"输出: {stdout[:200]}...")
                if stderr:
                    print(f"错误: {stderr[:200]}...")
                    
        except Exception as e:
            print(f"执行失败: {e}")

def check_available_tools():
    """检查可用的工具列表"""
    print("\n=== 检查可用工具 ===")
    
    # 尝试获取帮助信息
    try:
        result = subprocess.run(
            ["lark-mcp", "mcp", "--help"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.stdout:
            print("帮助信息:")
            print(result.stdout)
            
    except Exception as e:
        print(f"获取帮助信息失败: {e}")

def main():
    """主函数"""
    print("飞书MCP API测试工具")
    print("=" * 50)
    
    check_available_tools()
    test_mcp_tools()
    
    print("\n" + "=" * 50)
    print("测试完成")
    
    print("\n建议的解决方案:")
    print("1. 检查飞书应用是否有足够的权限")
    print("2. 确认用户访问令牌是否有效")
    print("3. 查看飞书开放平台文档中的MCP高级配置")
    print("4. 尝试使用OAuth模式获取用户授权")

if __name__ == "__main__":
    main()
