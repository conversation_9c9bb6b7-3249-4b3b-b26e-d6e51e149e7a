# 飞书文档图片下载记录

## 下载概览
- **文档来源**: 前端执行方案模版 - 20250714试行
- **文档ID**: B4LudmzddoWV7txI1rMc46VunMd
- **下载时间**: 2025年1月
- **下载工具**: 飞书MCP `drive.v1.media.batchGetTmpDownloadUrl`
- **图片总数**: 2张

## 图片详情

### 图片1: 功能点1交互图片
- **文件名**: `功能点1-交互图片.png`
- **原始token**: `Ge64bwGE5o4wPLxf1alcXE1cnfu`
- **尺寸**: 879 x 480 像素
- **原始URL**: https://yanxuan.nosdn.127.net/static-union/1752464546d2b5ca.png
- **临时下载链接**: `https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=OTlmOTA5YjE3NzIxZmQ4ZGMzZDhkM2MzNGQwN2ZkMWRfZmE0OTI1ZWNkYjgwYjVkYTVjN2FiMmQxNzIyNjI1ZTRfSUQ6NzUyNjc3NzczMDQ4MzQ1Mzk3MV8xNzU0MDE2MDgwOjE3NTQxMDI0ODBfVjM`
- **文件大小**: ~140KB
- **下载状态**: ✅ 成功
- **用途**: 线下门店退退货类型查看详情异常功能的交互图片

### 图片2: 功能点2交互图片
- **文件名**: `功能点2-交互图片.png`
- **原始token**: `LJwNbTsakodGOzxGbbOcUu5Snob`
- **尺寸**: 1002 x 486 像素
- **原始URL**: https://yanxuan.nosdn.127.net/static-union/1752464562e1e35f.png
- **临时下载链接**: `https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=MTNkZmFiYzIwZDU4NzUxMDUyMzU2M2VhN2YzYzFkNzVfZjAyNmYyYTdmNjg3YjQ2OTE3YjNlMWVkMDhjYzJkOTFfSUQ6NzUyNjc3NzgwMjA2Mzg3MjAyOF8xNzU0MDE2MDgwOjE3NTQxMDI0ODBfVjM`
- **文件大小**: ~140KB
- **下载状态**: ✅ 成功
- **用途**: 游戏序列化管理平台新增激活序列号列表功能的交互图片

## 技术实现

### 1. 获取图片token
从文档块结构中提取图片块的token信息：
```json
{
  "block_type": 27,
  "image": {
    "token": "Ge64bwGE5o4wPLxf1alcXE1cnfu",
    "width": 879,
    "height": 480,
    "align": 2
  }
}
```

### 2. 获取临时下载链接
使用飞书MCP工具：
```bash
drive.v1.media.batchGetTmpDownloadUrl
```

参数：
```json
{
  "file_tokens": [
    "Ge64bwGE5o4wPLxf1alcXE1cnfu",
    "LJwNbTsakodGOzxGbbOcUu5Snob"
  ],
  "useUAT": true
}
```

### 3. 下载图片
使用curl命令下载：
```bash
curl -o "功能点1-交互图片.png" "临时下载链接1"
curl -o "功能点2-交互图片.png" "临时下载链接2"
```

## 注意事项

1. **临时链接有效期**: 飞书的临时下载链接有时效性，通常24小时内有效
2. **权限要求**: 需要使用用户访问令牌(UAT)才能获取私有文档的图片
3. **文件格式**: 下载的图片保持原始格式(PNG)
4. **文件命名**: 使用中文命名便于识别图片用途

## 相关文件

- `前端执行方案模版-文档块结构.md` - 已更新为本地图片路径
- `文档块结构详情.json` - 包含完整的图片token信息
- `功能点1-交互图片.png` - 本地图片文件
- `功能点2-交互图片.png` - 本地图片文件

## 成功指标

✅ 成功获取2个图片的临时下载链接  
✅ 成功下载2张图片到本地  
✅ 更新文档中的图片引用为本地路径  
✅ 保留原始URL信息用于追溯  
✅ 创建完整的下载记录文档  
