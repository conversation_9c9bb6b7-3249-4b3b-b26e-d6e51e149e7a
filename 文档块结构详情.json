{"document_info": {"title": "前端执行方案模版 - 20250714试行", "document_id": "B4LudmzddoWV7txI1rMc46VunMd", "wiki_url": "https://kttfkmbfmy.feishu.cn/wiki/QmPXwm8XjipW7SkYFBuc8TtTngd", "extraction_time": "2025-01-01", "total_blocks": 80, "has_more": false}, "block_types": {"1": "页面块 (page)", "2": "文本块 (text)", "3": "一级标题 (heading1)", "4": "二级标题 (heading2)", "13": "有序列表 (ordered)", "14": "代码块 (code)", "27": "图片块 (image)", "31": "表格块 (table)", "32": "表格单元格 (table_cell)"}, "main_structure": [{"block_id": "B4LudmzddoWV7txI1rMc46VunMd", "block_type": 1, "content": "前端执行方案模版 - 20250714试行", "level": 0, "children_count": 10}, {"block_id": "Bxkfdd6qdoMLjDxgIc7cooSRngd", "block_type": 3, "content": "天枢任务", "level": 1, "parent_id": "B4LudmzddoWV7txI1rMc46VunMd"}, {"block_id": "WVBZd6ZqtojQ0Ixb9zfc4nVEnfe", "block_type": 2, "content": "https://yx.mail.netease.com/dubhe#/issues/detail?id=1078815&noReturn=true", "level": 1, "parent_id": "B4LudmzddoWV7txI1rMc46VunMd"}, {"block_id": "NS14dT2BMoZdJcxYl8HcbHftnDb", "block_type": 3, "content": "执行方案", "level": 1, "parent_id": "B4LudmzddoWV7txI1rMc46VunMd"}, {"block_id": "LvhAdzB1joiKPZxt8AqcfwfvnLd", "block_type": 4, "content": "功能点1", "level": 1, "parent_id": "B4LudmzddoWV7txI1rMc46VunMd"}, {"block_id": "RxVvdzDAPolUnfxDHtgclAptnAd", "block_type": 31, "content": "功能点1表格", "level": 1, "parent_id": "B4LudmzddoWV7txI1rMc46VunMd", "table_info": {"rows": 8, "columns": 2, "column_widths": [171, 688], "cells_count": 16}}, {"block_id": "F3WmdAikfoORH0x7C3oc619rn1g", "block_type": 4, "content": "功能点2", "level": 1, "parent_id": "B4LudmzddoWV7txI1rMc46VunMd"}, {"block_id": "FtMQddEAVoAOlCxKff8cuiavnNd", "block_type": 31, "content": "功能点2表格", "level": 1, "parent_id": "B4LudmzddoWV7txI1rMc46VunMd", "table_info": {"rows": 8, "columns": 2, "column_widths": [171, 688], "cells_count": 16}}, {"block_id": "QaYgdbN4boVYMKxGP5dcNTYDnQh", "block_type": 4, "content": "功能点3", "level": 1, "parent_id": "B4LudmzddoWV7txI1rMc46VunMd"}], "images": [{"block_id": "D1UTdgcOFomI29xe5cjc3qpBnwc", "token": "Ge64bwGE5o4wPLxf1alcXE1cnfu", "width": 879, "height": 480, "align": 2, "original_url": "https://yanxuan.nosdn.127.net/static-union/1752464546d2b5ca.png", "local_file": "./功能点1-交互图片.png", "file_size": 144897, "download_status": "success", "context": "功能点1交互图片"}, {"block_id": "Nn9qdN4hCoMxlDxxEDecyplcn1d", "token": "LJwNbTsakodGOzxGbbOcUu5Snob", "width": 1002, "height": 486, "align": 2, "original_url": "https://yanxuan.nosdn.127.net/static-union/1752464562e1e35f.png", "local_file": "./功能点2-交互图片.png", "file_size": 143545, "download_status": "success", "context": "功能点2交互图片"}], "code_blocks": [{"block_id": "UJdYdff3LoSRy4xgt8TccBCinKg", "language": 22, "language_name": "TypeScript", "wrap": true, "content_preview": "TypeScript接口定义，包含多个interface", "context": "功能点2的接口定义"}], "tables": [{"block_id": "RxVvdzDAPolUnfxDHtgclAptnAd", "name": "功能点1表格", "rows": 8, "columns": 2, "column_widths": [171, 688], "content": {"功能点名": "线下门店退退货类型查看详情异常", "类型": "修改", "cmdb/git信息": "serviceCode: yanxuan-kefu-admin", "交互图片URL地址": "包含图片", "URL/文件Path": "src/main/webapp/scripts/controllers/channel/offlineStoreAfterSaleCtrl.js", "待修改内容": "线下店订单列表查看退货的详情时，调用的后端接口缺失 expectTime 字段，修复下", "接口定义/Interface": "https://yx.mail.netease.com/bee/#/interface/list;serviceCode=yanxuan-kefu-admin;branchName=feature-20201201-itemfaq;selectedInterface=253951", "测试用例（可选）": "-"}}, {"block_id": "FtMQddEAVoAOlCxKff8cuiavnNd", "name": "功能点2表格", "rows": 8, "columns": 2, "column_widths": [171, 688], "content": {"功能点名": "游戏序列化管理平台新增激活序列号列表", "类型": "新建", "cmdb/git信息": "https://git.yx.netease.com/yanxuan/dhxy-workbench", "交互图片URL地址": "包含图片", "URL/文件Path": "https://test.yx.mail.netease.com/dhxy#/serialnumber/list", "待修改内容": "包含有序列表的详细步骤", "接口定义/Interface": "包含完整的TypeScript接口定义", "测试用例（可选）": "https://yx.mail.netease.com/dubhe#/testCase?menuId=900&suiteId=821 列表的第1条"}}], "links": ["https://yx.mail.netease.com/dubhe#/issues/detail?id=1078815&noReturn=true", "https://yx.mail.netease.com/bee/#/interface/list;serviceCode=yanxuan-kefu-admin;branchName=feature-20201201-itemfaq;selectedInterface=253951", "https://git.yx.netease.com/yanxuan/dhxy-workbench", "https://test.yx.mail.netease.com/dhxy#/serialnumber/list", "https://yx.mail.netease.com/dubhe#/testCase?menuId=900&suiteId=821"], "mcp_tool_info": {"tool_name": "docx_v1_documentBlock_list_lark-mcp", "parameters": {"document_id": "B4LudmzddoWV7txI1rMc46VunMd", "document_revision_id": -1, "page_size": 500, "useUAT": true}, "success": true, "extraction_complete": true}}