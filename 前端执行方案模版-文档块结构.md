# 前端执行方案模版 - 20250714试行

## 天枢任务

https://yx.mail.netease.com/dubhe#/issues/detail?id=1078815&noReturn=true

## 执行方案

### 功能点1

| 字段 | 内容 |
|------|------|
| **功能点名** | 线下门店退退货类型查看详情异常 |
| **类型** | 修改 |
| **cmdb/git信息（2选1）** | serviceCode: yanxuan-kefu-admin |
| **交互图片URL地址** | ![交互图片](https://yanxuan.nosdn.127.net/static-union/1752464546d2b5ca.png) |
| **URL/文件Path** | src/main/webapp/scripts/controllers/channel/offlineStoreAfterSaleCtrl.js |
| **待修改内容** | 线下店订单列表查看退货的详情时，调用的后端接口缺失 expectTime 字段，修复下 |
| **接口定义/Interface** | https://yx.mail.netease.com/bee/#/interface/list;serviceCode=yanxuan-kefu-admin;branchName=feature-20201201-itemfaq;selectedInterface=253951 |
| **测试用例（可选）** | - |

### 功能点2

| 字段 | 内容 |
|------|------|
| **功能点名** | 游戏序列化管理平台新增激活序列号列表 |
| **类型** | 新建 |
| **cmdb/git信息（2选1）** | https://git.yx.netease.com/yanxuan/dhxy-workbench |
| **交互图片URL地址** | ![交互图片](https://yanxuan.nosdn.127.net/static-union/1752464562e1e35f.png) |
| **URL/文件Path** | https://test.yx.mail.netease.com/dhxy#/serialnumber/list |
| **待修改内容** | 1. 修改侧边栏配置：<br/>   1. 删除原先全部配置<br/>   2. 游戏序列号管理->激活序列号<br/>   3. 链接为 #/serialnumber/list<br/>2. 删除原先全部appRoute路由，新增页面路径: #/serialnumber/list<br/>3. 列表接口/xhr/dhxy/admin/codeInfo/pageQuery.json根据interface，严格参照交互稿完成页面内容 |
| **接口定义/Interface** | 见下方TypeScript接口定义 |
| **测试用例（可选）** | https://yx.mail.netease.com/dubhe#/testCase?menuId=900&suiteId=821 列表的第1条 |

#### TypeScript接口定义

```typescript
/**
 * MissaResponse«SearchResult«DhxyCodeInfoListVO»»
 */
export interface ApifoxModel {
    body?: SearchResultDhxyCodeInfoListVO;
    headers?: HttpHeaders;
    httpStatus?: number | null;
    inputStream?: { [key: string]: any };
    missaResponseEntity?: MissaResponseEntitySearchResultDhxyCodeInfoListVO;
    [property: string]: any;
}

/**
 * SearchResult«DhxyCodeInfoListVO»
 *
 * 响应数据,object格式，非必须
 * <p>
 * 自定义的响应内容结构
 * </p>
 */
export interface SearchResultDhxyCodeInfoListVO {
    /**
     * 分页信息
     */
    pagination?: Pagination;
    /**
     * 结果列表
     */
    result?: DhxyCodeInfoListVO[] | null;
    [property: string]: any;
}

/**
 * 分页信息
 *
 * Pagination
 */
export interface Pagination {
    /**
     * 当前页
     */
    page?: number | null;
    /**
     * 页大小
     */
    size?: number | null;
    /**
     * 结果总数
     */
    total?: number | null;
    /**
     * 总页数
     */
    totalPage?: number | null;
    [property: string]: any;
}

/**
 * com.netease.yx.act.admin.vo.dhxy.DhxyCodeInfoListVO
 *
 * DhxyCodeInfoListVO
 */
export interface DhxyCodeInfoListVO {
    /**
     * 激活码总数
     */
    actCodeSum?: number | null;
    /**
     * 活动编号(如年份)
     */
    actNumber?: null | string;
    /**
     * 激活码类型<br/>
     * 1：预约码(预沟序列号)<br/>
     * 2：激活码(激活序列号)
     */
    codeType?: number;
    /**
     * 创建时间
     */
    createTime?: number | null;
    /**
     * 版本<br/>
     * 1：经典版<br/>
     * 2：免费版
     */
    edition?: number;
    /**
     * 主键，新建时不传，更新时必传
     */
    id?: number | null;
    /**
     * 物品类型<br/>
     * 1：分享版<br/>
     * 2：真爱版<br/>
     * 3：白金版<br/>
     * 4：典藏版
     */
    itemType?: number;
    /**
     * 预约码未使用数
     */
    resCodeNotUsedSum?: number | null;
    /**
     * 预约码总数
     */
    resCodeSum?: number | null;
    /**
     * 预约码已使用数
     */
    resCodeUsedSum?: number | null;
    [property: string]: any;
}

/**
 * HttpHeaders
 */
export interface HttpHeaders {
    key?: string[];
    [property: string]: any;
}

/**
 * MissaResponseEntity«SearchResult«DhxyCodeInfoListVO»»
 */
export interface MissaResponseEntitySearchResultDhxyCodeInfoListVO {
    /**
     * 子响应码，number格式,非必须
     * <p>
     * 一般在业务异常或有业务特殊含义时使用
     * </p>
     */
    code?: number | null;
    /**
     * 响应数据,object格式，非必须
     * <p>
     * 自定义的响应内容结构
     * </p>
     */
    data?: SearchResultDhxyCodeInfoListVO;
    /**
     * 内部错误详细信息，MissaResponseError格式，非必须
     * <p>
     * 可供调用方快速定位问题，非必须，嵌套格式可追溯到源
     * </p>
     */
    error?: MissaResponseErrorEntity;
    /**
     * Msg消息，字符串格式，非必须
     * <p>
     * 可供调用方直接对终端用户输出和使用的业务报错信息 业务异常必须，其它非必须，不允许在此存放堆栈信息等无意义信息
     * </p>
     */
    message?: null | string;
    [property: string]: any;
}

/**
 * 内部错误详细信息，MissaResponseError格式，非必须
 * <p>
 * 可供调用方快速定位问题，非必须，嵌套格式可追溯到源
 * </p>
 *
 * MissaResponseErrorEntity
 *
 * 嵌套的error结构，非必须
 */
export interface MissaResponseErrorEntity {
    /**
     * 错误的详细说明，非必须
     */
    description?: string;
    /**
     * 嵌套的error结构，非必须
     */
    error?: MissaResponseErrorEntity;
    /**
     * 更加详细的错误异常码，非必须
     */
    errorCode?: number;
    [property: string]: any;
}
```

### 功能点3

...

---

## 文档块结构信息

### 块类型统计
- **页面块** (block_type: 1): 1个
- **文本块** (block_type: 2): 多个
- **一级标题** (block_type: 3): 2个
- **二级标题** (block_type: 4): 3个
- **有序列表** (block_type: 13): 多个
- **代码块** (block_type: 14): 1个
- **图片块** (block_type: 27): 2个
- **表格块** (block_type: 31): 2个
- **表格单元格** (block_type: 32): 多个

### 文档结构层级
```
B4LudmzddoWV7txI1rMc46VunMd (根文档)
├── Bxkfdd6qdoMLjDxgIc7cooSRngd (天枢任务)
├── WVBZd6ZqtojQ0Ixb9zfc4nVEnfe (任务链接)
├── NS14dT2BMoZdJcxYl8HcbHftnDb (执行方案)
├── LvhAdzB1joiKPZxt8AqcfwfvnLd (功能点1)
├── RxVvdzDAPolUnfxDHtgclAptnAd (功能点1表格)
├── F3WmdAikfoORH0x7C3oc619rn1g (功能点2)
├── FtMQddEAVoAOlCxKff8cuiavnNd (功能点2表格)
├── XOItd2zGYoRPyoxout5c95LSnvg (空文本)
├── QaYgdbN4boVYMKxGP5dcNTYDnQh (功能点3)
└── MSmhdiC2popvHwx2yhpcAQ90n0w (省略号)
```

### 详细块信息

#### 主要块ID和类型
1. **B4LudmzddoWV7txI1rMc46VunMd** - 页面根块 (block_type: 1)
2. **Bxkfdd6qdoMLjDxgIc7cooSRngd** - "天枢任务" 一级标题 (block_type: 3)
3. **WVBZd6ZqtojQ0Ixb9zfc4nVEnfe** - 任务链接文本 (block_type: 2)
4. **NS14dT2BMoZdJcxYl8HcbHftnDb** - "执行方案" 一级标题 (block_type: 3)
5. **LvhAdzB1joiKPZxt8AqcfwfvnLd** - "功能点1" 二级标题 (block_type: 4)
6. **RxVvdzDAPolUnfxDHtgclAptnAd** - 功能点1表格 (block_type: 31)
7. **F3WmdAikfoORH0x7C3oc619rn1g** - "功能点2" 二级标题 (block_type: 4)
8. **FtMQddEAVoAOlCxKff8cuiavnNd** - 功能点2表格 (block_type: 31)
9. **QaYgdbN4boVYMKxGP5dcNTYDnQh** - "功能点3" 二级标题 (block_type: 4)

#### 表格结构详情
- **表格1** (功能点1): 8行2列，包含16个单元格
- **表格2** (功能点2): 8行2列，包含16个单元格
- 列宽设置: [171, 688] 像素
- 支持单元格合并功能

#### 图片信息
- **图片1**: token=Ge64bwGE5o4wPLxf1alcXE1cnfu, 尺寸=879x480
- **图片2**: token=LJwNbTsakodGOzxGbbOcUu5Snob, 尺寸=1002x486

#### 代码块信息
- 语言类型: TypeScript (language: 22)
- 支持代码换行 (wrap: true)
- 包含完整的接口定义

### 使用的MCP工具
- **工具名称**: `docx_v1_documentBlock_list_lark-mcp`
- **参数**:
  - document_id: B4LudmzddoWV7txI1rMc46VunMd
  - document_revision_id: -1 (最新版本)
  - page_size: 500
  - useUAT: true

### 原始文档链接
- **Wiki链接**: https://kttfkmbfmy.feishu.cn/wiki/QmPXwm8XjipW7SkYFBuc8TtTngd
- **文档ID**: B4LudmzddoWV7txI1rMc46VunMd
- **获取时间**: 2025年1月
- **总块数**: 约80个块（包括所有子块）
